#!/usr/bin/env python3
"""
音频监控功能测试脚本
用于测试OBS去重器中的音频监控相关功能
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QComboBox
from PyQt5.QtCore import Qt, QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_module import MainWindow
    print("✅ 成功导入主模块")
except ImportError as e:
    print(f"❌ 导入主模块失败: {e}")
    sys.exit(1)

class AudioMonitorTest(QWidget):
    """音频监控测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("音频监控功能测试")
        self.setGeometry(200, 200, 400, 300)
        
        # 创建主窗口实例（但不显示）
        self.main_window = MainWindow()
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("音频监控功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 音频设备信息
        self.device_info_label = QLabel("正在获取音频设备信息...")
        layout.addWidget(self.device_info_label)
        
        # 测试按钮
        self.test_init_button = QPushButton("测试音频监控初始化")
        self.test_init_button.clicked.connect(self.test_audio_init)
        layout.addWidget(self.test_init_button)
        
        self.test_devices_button = QPushButton("测试音频设备刷新")
        self.test_devices_button.clicked.connect(self.test_device_refresh)
        layout.addWidget(self.test_devices_button)
        
        self.test_monitoring_button = QPushButton("测试音频监控启动")
        self.test_monitoring_button.clicked.connect(self.test_monitoring)
        layout.addWidget(self.test_monitoring_button)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: blue; margin: 10px;")
        layout.addWidget(self.status_label)
        
        # 延迟初始化
        QTimer.singleShot(1000, self.delayed_init)
        
    def delayed_init(self):
        """延迟初始化"""
        try:
            # 测试音频监控初始化
            self.main_window.init_audio_monitoring()
            self.update_device_info()
        except Exception as e:
            self.status_label.setText(f"初始化失败: {e}")
            self.status_label.setStyleSheet("color: red; margin: 10px;")
    
    def update_device_info(self):
        """更新设备信息显示"""
        try:
            if hasattr(self.main_window, 'audio_devices') and self.main_window.audio_devices:
                device_count = len(self.main_window.audio_devices)
                self.device_info_label.setText(f"找到 {device_count} 个音频监控设备")
            else:
                self.device_info_label.setText("未找到音频监控设备")
        except Exception as e:
            self.device_info_label.setText(f"获取设备信息失败: {e}")
    
    def test_audio_init(self):
        """测试音频监控初始化"""
        try:
            self.status_label.setText("正在测试音频监控初始化...")
            self.status_label.setStyleSheet("color: orange; margin: 10px;")
            
            # 重新初始化
            self.main_window.init_audio_monitoring()
            
            if hasattr(self.main_window, 'pyaudio') and self.main_window.pyaudio:
                self.status_label.setText("✅ 音频监控初始化成功")
                self.status_label.setStyleSheet("color: green; margin: 10px;")
            else:
                self.status_label.setText("❌ 音频监控初始化失败")
                self.status_label.setStyleSheet("color: red; margin: 10px;")
                
            self.update_device_info()
            
        except Exception as e:
            self.status_label.setText(f"❌ 测试失败: {e}")
            self.status_label.setStyleSheet("color: red; margin: 10px;")
    
    def test_device_refresh(self):
        """测试设备刷新"""
        try:
            self.status_label.setText("正在刷新音频设备...")
            self.status_label.setStyleSheet("color: orange; margin: 10px;")
            
            # 刷新设备
            if hasattr(self.main_window, 'refresh_audio_devices'):
                self.main_window.refresh_audio_devices()
                self.status_label.setText("✅ 设备刷新完成")
                self.status_label.setStyleSheet("color: green; margin: 10px;")
            else:
                self.status_label.setText("❌ 刷新方法不存在")
                self.status_label.setStyleSheet("color: red; margin: 10px;")
                
            self.update_device_info()
            
        except Exception as e:
            self.status_label.setText(f"❌ 刷新失败: {e}")
            self.status_label.setStyleSheet("color: red; margin: 10px;")
    
    def test_monitoring(self):
        """测试音频监控启动"""
        try:
            self.status_label.setText("正在测试音频监控启动...")
            self.status_label.setStyleSheet("color: orange; margin: 10px;")
            
            # 检查是否有可用设备
            if not hasattr(self.main_window, 'audio_devices') or not self.main_window.audio_devices:
                self.status_label.setText("❌ 没有可用的音频设备")
                self.status_label.setStyleSheet("color: red; margin: 10px;")
                return
            
            # 使用第一个设备进行测试
            test_device = self.main_window.audio_devices[0]
            
            # 测试启动监控
            self.main_window.start_pyaudio_device_monitoring('test', test_device)
            
            # 检查监控是否启动
            if 'test' in self.main_window.audio_monitors:
                self.status_label.setText("✅ 音频监控启动成功")
                self.status_label.setStyleSheet("color: green; margin: 10px;")
                
                # 3秒后停止监控
                QTimer.singleShot(3000, lambda: self.stop_test_monitoring())
            else:
                self.status_label.setText("❌ 音频监控启动失败")
                self.status_label.setStyleSheet("color: red; margin: 10px;")
                
        except Exception as e:
            self.status_label.setText(f"❌ 监控测试失败: {e}")
            self.status_label.setStyleSheet("color: red; margin: 10px;")
    
    def stop_test_monitoring(self):
        """停止测试监控"""
        try:
            self.main_window.stop_audio_monitoring('test')
            self.status_label.setText("✅ 测试监控已停止")
            self.status_label.setStyleSheet("color: blue; margin: 10px;")
        except Exception as e:
            self.status_label.setText(f"❌ 停止监控失败: {e}")
            self.status_label.setStyleSheet("color: red; margin: 10px;")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置样式
    app.setStyleSheet("""
        QWidget {
            background-color: #f0f0f0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            margin: 5px;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        QLabel {
            padding: 5px;
        }
    """)
    
    # 创建测试窗口
    test_window = AudioMonitorTest()
    test_window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
